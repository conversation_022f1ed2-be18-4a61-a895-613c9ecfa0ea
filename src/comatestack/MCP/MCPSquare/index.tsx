import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useEffect} from 'react';
import {FetchMCPServersProvider} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import MCPServerList from '@/components/MCP/MCPServerCard/MCPServerList';
import MCPServerFilter, {FilterValues} from '@/components/MCP/MCPServerFilter';
import {ServerType} from '@/components/MCP/MCPServerFilter/ServerTypeFilter';
import {ServerProtocol} from '@/components/MCP/MCPServerFilter/ServerProtocolFilter';
import {Order} from '@/components/MCP/MCPServerFilter/CompositeFilter';
import {apiGetAllZones} from '@/api/mcp';
import RegionNavigation from './RegionNavigation';

const Container = styled(Flex)`
    width: 100%;
    height: calc(100vh - 48px);
    padding: 0 20px 16px;
    overflow: auto;
`;

const initialTabFormValue = {tab: 'all'};
const initialFilterFormValue: FilterValues = {
    serverSourceType: ServerType.ALL,
    serverProtocolType: ServerProtocol.ALL,
    labels: [-2],
    order: Order.DEFAULT,
};
const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
};

const MCPSquare = () => {
    useEffect(
        () => {
            apiGetAllZones();
        },
        []
    );
    return (
        <FetchMCPServersProvider initParams={initialSearchParams} platformType="hub">
            <Container vertical gap={16} id="scrollableDiv">
                <RegionNavigation />
                <MCPServerFilter
                    style={{marginTop: '16px', position: 'sticky', top: 0, zIndex: 2, backgroundColor: '#fff'}}
                    initialFilterFormValue={initialFilterFormValue}
                    initialTabFormValue={initialTabFormValue}
                />
                <MCPServerList scrollableTarget="scrollableDiv" />
            </Container>
        </FetchMCPServersProvider>

    );
};

export default MCPSquare;
