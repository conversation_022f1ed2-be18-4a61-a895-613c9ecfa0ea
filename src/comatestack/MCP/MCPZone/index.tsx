import {useEffect, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {useRequestCallback} from 'huse';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {useMCPZoneId} from '@/components/MCP/hooks';
import {MCPZoneLink} from '@/links/mcp';
import {
    FetchMCPServersProvider,
} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import MCPServerFilter, {FilterValues} from '@/components/MCP/MCPServerFilter';
import MCPServerList from '@/components/MCP/MCPServerCard/MCPServerList';
import {ServerType} from '@/components/MCP/MCPServerFilter/ServerTypeFilter';
import {Order} from '@/components/MCP/MCPServerFilter/CompositeFilter';
import {ServerProtocol} from '@/components/MCP/MCPServerFilter/ServerProtocolFilter';
import {apiGetMCPZoneDetail} from '@/api/mcp';
import {OperationZoneHeader} from './OperationZoneHeader';

const DEVELOP_SECTION_ID = '';

const initialTabFormValue = {tab: 'all'};
const initialFilterFormValue: FilterValues = {
    serverSourceType: ServerType.ALL,
    serverProtocolType: ServerProtocol.ALL,
    labels: [-2],
    order: Order.DEFAULT,
};
const initialSearchParams = {
    ...initialTabFormValue,
    ...initialFilterFormValue,
};

const Container = styled.div`
    width: 100%;
    // padding: 0 20px 16px;
    overflow: auto;
`;

const paddingCss = css`
    padding: 0 20px 16px;
`;

const MCPZone = () => {
    const mcpZoneId = useMCPZoneId();
    const navigate = useNavigate();
    const [fetchZone, {data: zone}] = useRequestCallback(apiGetMCPZoneDetail, mcpZoneId);
    useEffect(
        () => {
            if (!mcpZoneId) {
                navigate(MCPZoneLink.toUrl({zoneId: DEVELOP_SECTION_ID}));
            }
        },
        [mcpZoneId, navigate]
    );
    useEffect(
        () => {
            if (mcpZoneId) {
                fetchZone();
            }
        },
        [mcpZoneId, fetchZone]
    );
    const initParams = useMemo(
        () => {
            if (mcpZoneId) {
                return {
                    ...initialSearchParams,
                    zoneId: mcpZoneId,
                };
            }
            return initialSearchParams;
        },
        [mcpZoneId]
    );
    return (
        <FetchMCPServersProvider initParams={initParams} platformType="zone">
            <Container id="scrollableDiv">
                <OperationZoneHeader zone={zone} />
                <div className={paddingCss}>
                    <MCPServerFilter
                        style={{marginTop: '16px'}}
                        initialFilterFormValue={initialFilterFormValue}
                        initialTabFormValue={initialTabFormValue}
                    />
                    <MCPServerList scrollableTarget="scrollableDiv" />
                </div>
            </Container>
        </FetchMCPServersProvider>
    );
};

export default MCPZone;
