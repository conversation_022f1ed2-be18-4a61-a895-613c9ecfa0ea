import {LeftOutlined} from '@ant-design/icons';
import {Flex, Typography} from 'antd';
import {ReactNode, useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {MCPSquareLink} from '@/links/mcp';

interface Props {
  children: ReactNode;
}

export const ZoneHeaderBreadcrumb = ({children}: Props) => {
    const navigate = useNavigate();
    const back = useCallback(
        () => {
            navigate(MCPSquareLink.toUrl());
        },
        [navigate]
    );
    return (
        <Flex>
            <LeftOutlined onClick={back} />
            <Typography.Title style={{marginLeft: '10px', fontSize: '16px'}}>{children}</Typography.Title>
        </Flex>
    );
};
