/* eslint-disable max-lines */
import {Flex, Typography} from 'antd';
import {SettingOutlined} from '@ant-design/icons';
import {ReactNode, useCallback, useEffect, useState} from 'react';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import iconsMap from '@/icons/mcp';
import {MCPZoneDetail} from '@/api/mcp';
import bg from '@/assets/mcp/mcpzone-bg1.png';
import {UserAvatarList} from '@/components/MCP/UserAvatarList';
import {useFetchMCPServersContext} from '@/components/MCP/MCPServerCard/FetchMCPServersProvider';
import {ZoneHeaderBreadcrumb} from './ZoneHeaderBreadcrumb';
import {ZoneTabs} from './ZoneTabs';

interface Props {
  zone: MCPZoneDetail;
}

interface PropertyProps{
  name: string;
  value: ReactNode;
}

const HeaderProperty = ({name, value}: PropertyProps) => {
    return (
        <Flex>
            <Typography.Text style={{color: '#5C5C5C'}}>{name}</Typography.Text>
            <div style={{marginLeft: '24px'}}>
                {value}
            </div>
        </Flex>
    );
};

interface StatisticsProps {
  label: string;
  icon: ReactNode;
  number: string | number;
}

const StyledStatisticsItem = styled(Flex)`
  border-radius: 4px;
  background: #fff;
  padding: 18px;
  width: 300px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.03);
`;

const ZoneStatistics = ({label, icon, number}: StatisticsProps) => {
    return (
        <StyledStatisticsItem justify="space-between">
            <Flex>
                <span>{icon}</span>
                <span style={{color: '#5C5C5C', marginLeft: '6px'}}>{label}</span>
            </Flex>
            <span>{number}</span>
        </StyledStatisticsItem>
    );
};

const extractZoneBaseProperties = (zone: MCPZoneDetail) => {
    return {
        'id': zone.id,
        'name': zone.name,
        'type': zone.type,
        'description': zone.description,
        'announcement': zone.announcement,
        'publisher': zone.publisher,
        'department': zone.department,
        'icon': zone.icon,
        'serverCount': zone.serverCount,
        'subCount': zone.subCount,
        'commentCount': zone.commentCount,
    };
};

type FormatedZone = MCPZoneDetail | {name: string, childZones: MCPZoneDetail[]} | null;

const useZoneProperties = (zone?: MCPZoneDetail): FormatedZone => {
    const [formated, setFormated] = useState(null);
    useEffect(
        () => {
            if (zone) {
                if (zone.childZones.length > 0) {
                    setFormated(() => {
                        const childZones = [{
                            ...extractZoneBaseProperties(zone),
                            name: '全部',

                        }];
                        zone.childZones.forEach(child => {
                            childZones.push(extractZoneBaseProperties(child));
                        });
                        return {
                            name: zone.name,
                            childZones,
                        };
                    });
                } else {
                    setFormated({
                        ...extractZoneBaseProperties(zone),
                        childZones: [],
                    });
                }
            }
        },
        [zone]
    );
    return formated;
};

const Container = styled(Flex)`
    padding: 24px;
    background: linear-gradient(-45deg, rgba(233, 248, 255, 1), #fff);
`;

export const OperationZoneHeader = ({zone}: Props) => {
    const {changeSearchParams} = useFetchMCPServersContext();
    const [currentZone, setCurrentZone] = useState<MCPZoneDetail|null>(null);
    const formatedZone = useZoneProperties(zone);
    useEffect(
        () => {
            if (formatedZone) {
                setCurrentZone(
                    formatedZone.childZones.length > 0
                        ? formatedZone.childZones[0] : formatedZone as MCPZoneDetail);
            }
        },
        [formatedZone]
    );
    const changeZone = useCallback(
        (zone: MCPZoneDetail) => {
            setCurrentZone(zone);
            changeSearchParams({zoneId: zone.id});
        },
        [changeSearchParams]

    );
    return (
        <Container vertical gap="24px">
            <Flex justify="space-between">
                <ZoneHeaderBreadcrumb>{formatedZone?.name}</ZoneHeaderBreadcrumb>
                {/* 管理占位 */}
                <Button style={{display: 'none'}} color="default" variant="link" icon={<SettingOutlined />} href="">
                    管理
                </Button>
            </Flex>
            {
                formatedZone?.childZones && formatedZone.childZones.length > 0
                    && <ZoneTabs zones={formatedZone.childZones} setZoneContent={changeZone} />
            }
            <Flex justify="space-between">
                <Flex>
                    <div style={{
                        backgroundColor: 'rgba(233, 248, 255, 1)',
                        width: '360px',
                        height: '180px',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: '1px solid #dddddd',
                    }}
                    >
                        <img
                            src={bg}
                            // style={{
                            //     width: '100px',
                            //     height: '100px',
                            // }}
                        />
                    </div>
                    <Flex vertical gap="16px" style={{marginLeft: '24px'}}>
                        <Typography.Text>{currentZone?.description}</Typography.Text>
                        <HeaderProperty name="出品人" value={currentZone?.department} />
                        <HeaderProperty name="联系人" value={<UserAvatarList users={currentZone?.publisher} max={2} />} />
                    </Flex>
                </Flex>
                <Flex vertical gap="12px">
                    <ZoneStatistics
                        label="MCP数"
                        icon={<iconsMap.mcp />}
                        number={currentZone?.serverCount}
                    />
                    <ZoneStatistics
                        label="订阅量"
                        icon={<iconsMap.subscribe2 />}
                        number={currentZone?.subCount}
                    />
                    <ZoneStatistics
                        label="评论数"
                        icon={<iconsMap.comment />}
                        number={currentZone?.commentCount}
                    />
                </Flex>
            </Flex>
        </Container>
    );
};
