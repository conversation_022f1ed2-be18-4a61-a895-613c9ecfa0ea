import {useCallback, useMemo} from 'react';
import {Tabs} from 'antd';
import styled from '@emotion/styled';
import {MCPZoneDetail} from '@/api/mcp';

interface Props {
  zones: Array<Omit<MCPZoneDetail, 'children'>>;
  setZoneContent: (zone: MCPZoneDetail) => void;
}

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-tab{
        position: relative;
        top: 1px;
        background-color: transparent !important;
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        border-bottom: 1px solid #dfdfdf !important;
    }
    .ant-5-tabs-tab-active{
        border-top-color: #0083FF !important;
        border-left-color: #0083FF !important;
        border-right-color: #0083FF !important;
        border-bottom: 1px solid transparent !important;
    }
`;

export const ZoneTabs = ({zones, setZoneContent}: Props) => {
    const tabItems = useMemo(
        () => {
            return zones.map(zone => ({
                label: zone.name,
                key: zone.id,
            }));
        },
        [zones]
    );
    const onChange = useCallback(
        (id: string) => {
            const cur = zones.find(i => i.id === id);
            if (cur) {
                setZoneContent(cur);
            }
        },
        [setZoneContent, zones]
    );
    return (
        <StyledTabs
            onChange={onChange}
            type="card"
            items={tabItems}
        />
    );
};
