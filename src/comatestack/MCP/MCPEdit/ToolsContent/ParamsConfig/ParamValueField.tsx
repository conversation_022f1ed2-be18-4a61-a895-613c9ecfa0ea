import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useMemo} from 'react';
import {RuleObject} from 'antd/es/form';
import {BaseParam} from '@/types/mcp/mcp';
import {useActiveTool} from '../hooks';
import {getParamValueFieldPath, useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';
import {isParamRelated} from './index';

const ParamValueField = ({basePath, record}: {
    basePath: Path;
    record: BaseParam;
}) => {
    const {getToolApiConfigData} = useToolParamsConfigContext();
    const {activeToolIndex} = useActiveTool();
    const paramsList: BaseParam[] = Form.useWatch(['tools', activeToolIndex, 'toolParams', 'toolParams']);
    const disabled = isParamRelated(record.key, paramsList);
    const path = useMemo(
        () => {
            const allParams = getToolApiConfigData(activeToolIndex);
            const path = getParamValueFieldPath({basePath, record, allParams});
            return path;
        },
        [getToolApiConfigData, activeToolIndex, basePath, record]
    );
    const rules = useMemo(
        () => {
            return [
                {
                    validator: (_: RuleObject, value: string) => {
                        if (record.required && !disabled && !value) {
                            return Promise.reject(new Error('必填'));
                        }
                        return Promise.resolve();
                    },
                },
            ];
        },
        [disabled, record.required]
    );
    if (record.canRelate === false) {
        return null;
    }
    return (
        <Form.Item
            style={{marginBottom: 0}}
            name={path}
            rules={rules}
        >
            <Input placeholder="请输入" disabled={disabled} />
        </Form.Item>
    );
};

export default ParamValueField;

