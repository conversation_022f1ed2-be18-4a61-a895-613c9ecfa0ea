import {Flex, Form, Input} from 'antd';
import CopyButton from '@/components/Chat/Element/ToolMCPCall/CopyButton';
import {useActiveTool} from '../hooks';

const ResponseTemplate = (props: {template: string | null}) => {
    const {template} = props;
    const {activeToolIndex} = useActiveTool();

    const placeholder = `
    JSON示例
        {
            "id": {{$.data[0].id}},
            "name": {{$.data[0].name}}
        }
    Markdown示例
        **卡片标题**: {{$.data[0].title}}
        **卡片内容**: {{$.data[0].content}}
    `;
    return (
        <div style={{position: 'relative'}}>
            <Flex justify="end">
                <CopyButton text={template} />
            </Flex>
            <Form.Item
                name={['tools', activeToolIndex, 'toolConf', 'responseTemplate']}
                style={{flex: 1}}
            >
                <Input.TextArea rows={6} defaultValue={template} placeholder={placeholder} />
            </Form.Item>
        </div>
    );
};

export default ResponseTemplate;
