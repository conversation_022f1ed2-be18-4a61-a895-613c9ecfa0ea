import {QuestionCircleOutlined} from '@ant-design/icons';
import {Path} from '@panda-design/path-form';
import {useSearchParams} from '@panda-design/router';
import {Flex, Form, Radio, Tooltip} from 'antd';
import {useMemo} from 'react';

interface Props {
    path?: Path;
    disabled?: boolean;
}

const ProtocolField = ({path = [], disabled}: Props) => {
    const {type: serverSourceType} = useSearchParams();
    const options = useMemo(
        () => [
            {
                label: 'Streamable HTTP',
                value: 'Streamable_HTTP',
                hidden: serverSourceType === 'script',

            },
            {
                label: (
                    <Flex>
                        <span>SSE</span>
                        {serverSourceType === 'openapi' && (
                            <Tooltip title="已弃用">
                                <QuestionCircleOutlined style={{marginLeft: 4}} />
                            </Tooltip>
                        )}
                    </Flex>
                ),
                value: 'SSE',
                disabled: serverSourceType === 'openapi',
                hidden: serverSourceType === 'script',
            },
            {
                label: 'STDIO',
                value: 'STDIO',
                hidden: serverSourceType === 'openapi',
            },
        ].filter(option => !option.hidden),
        [serverSourceType]
    );
    return (
        <Form.Item
            label="协议"
            name={[...path, 'serverProtocolType']}
            rules={[{required: true, message: '请选择协议类型'}]}
        >
            <Radio.Group options={options} disabled={disabled} />
        </Form.Item>
    );
};

export default ProtocolField;
