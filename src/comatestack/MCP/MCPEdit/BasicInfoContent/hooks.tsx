import {Form} from 'antd';
import {isDate} from 'date-fns';
import {isArray, isString, keys} from 'lodash';
import {useCallback} from 'react';
import {BaseParam} from '@/types/mcp/mcp';

export const getDataType = (value: any) => {
    if (isDate(value)) {
        return 'date';
    }
    return isString(value)
        ? 'string'
        : isArray(value)
            ? 'array'
            : keys(value).length > 0
                ? 'object'
                : typeof value;
};

interface Params {
    name: string;
    dataType?: string;
    dataValue?: any;
}

// eslint-disable-next-line complexity
export const getDefaultItem: (params: Params) => Record<string, any> = ({name, dataType, dataValue}: Params) => {
    if (dataType) {
        switch (dataType) {
            case 'array':
                return {
                    [name]: getDataType(dataValue) === 'array' ? dataValue : [],
                };
            case 'object':
                return {
                    [name]: getDataType(dataValue) === 'object' ? dataValue : {},
                };
            case 'string':
                return {
                    [name]: getDataType(dataValue) === 'string' ? dataValue : '',
                };
            case 'number':
                return {
                    [name]: getDataType(dataValue) === 'number' ? dataValue : 0,
                };
            default:
                return {
                    [name]: dataValue ?? '',
                };
        }
    } else {
        return {
            name,
            dataType: getDataType(dataValue),
        };
    }
};

// 从serverConfig中提取serverParams，以创建标准MCP
export const extractServerParams = (serverConfig: string, serverProtocolType: string) => {
    try {
        if (serverProtocolType === 'SSE' || serverProtocolType === 'STDIO'
            || serverProtocolType === 'Streamable_HTTP') {
            const parserServerConfig = JSON.parse(serverConfig);
            const serverKey = keys(parserServerConfig.mcpServers)[0];
            const config = parserServerConfig?.mcpServers?.[serverKey];
            const env = config?.env ?? {};
            const headers = config?.headers ?? {};
            // SSE和Streamable_HTTP类MCP的配置里只能有headers，stdio的配置只能有env
            const targetParams = (serverProtocolType === 'SSE' || serverProtocolType === 'Streamable_HTTP')
                ? headers : env;
            const newServerParams = Object.entries(targetParams).map(
                ([key, value], index) => {
                    return {
                        // 类型默认先给string，value本质是占位符或描述信息，与类型无关
                        dataType: 'string',
                        key: index,
                        name: key,
                        required: true,
                        description: value ?? '',
                    };
                }
            );
            return newServerParams;
        }
        return [];

    } catch (e) {
        console.error(e);
        return [];
    }
};

export const useFillServerParamsByServerConfig = () => {
    const {setFieldValue} = Form.useFormInstance();
    const serverConfig = Form.useWatch(['serverInfo', 'serverConf', 'serverConfig']);
    const serverParams: BaseParam[] = Form.useWatch(['serverInfo', 'serverParams']);
    const serverProtocolType = Form.useWatch(['serverInfo', 'serverProtocolType']);
    const fillServerParamsByServerConfig = useCallback(
        () => {
            const newServerParams = extractServerParams(serverConfig, serverProtocolType);
            const mergedParams = newServerParams.map(newParam => {
                const valueFromServerParams = serverParams?.find((item, index) => index === newParam.key);
                if (valueFromServerParams) {
                    return {
                        ...valueFromServerParams,
                        name: newParam.name,
                        description: newParam.description,
                    };
                }
                return {
                    dataType: newParam.dataType,
                    key: newParam.key,
                    name: newParam.name,
                    description: newParam.description,
                };
            });
            setFieldValue(
                ['serverInfo', 'serverParams'],
                mergedParams
            );
        },
        [serverConfig, serverProtocolType, setFieldValue, serverParams]
    );
    return fillServerParamsByServerConfig;
};
