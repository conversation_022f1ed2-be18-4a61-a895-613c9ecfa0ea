// import {IconInfoflow, IconOutlook} from '@baidu/devops-icons';
// import styled from '@emotion/styled';

// export interface IUser {
//     /** 百度账号 */
//     baiduHi?: string;
//     /** 邮箱 */
//     email?: string;
//     /** 用户名 */
//     userName: string;
//     /** 中文名 */
//     chineseName: string;
//     /** 部门名称 */
//     departmentName: string;
//     /** 职位角色信息 */
//     positionRoles: string[] | null;
// }

// const CardRoot = styled(Flex)`
//     padding: 12px 12px;
//     border-radius: 4px;
// `;

// const UserContactContainer = styled(Flex)`
//     > a {
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         height: 24px;
//         width: 32px;
//         background-color: var(--devops-gray-2);
//         color: var(--devops-gray-9);

//         &:hover {
//             background-color: var(--devops-gray-4);
//         }
//     }

//     --radius: 12px;
//     > a {
//         border-radius: var(--radius);
//     }

//     > a:first-of-type:not(:last-child) {
//         position: relative;
//         border-radius: var(--radius) 0 0 var(--radius);

//         &:after {
//             content: '';
//             position: absolute;
//             right: 0;
//             width: 1px;
//             height: 12px;
//             background: var(--devops-gray-5);
//         }

//         + a {
//             border-radius: 0 var(--radius) var(--radius) 0;
//         }
//     }
// `;


// export function UserCard({user}: {user: IUser, log?: (label: 'email' | 'ruliu') => void}) {
//     return (
//         <CardRoot centered>
//             <User
//                 key={user.userName}
//                 avatarSize="default"
//                 userName={user.userName}
//                 chineseName={user.chineseName}
//                 departmentName={user.departmentName}
//                 positionRoles={user.positionRoles}
//             />
//             <UserContactContainer>
//                 {user.email && (
//                     <a href={`mailto:${user.email}`}>
//                         <IconOutlook />
//                     </a>
//                 )}
//                 {user.baiduHi && (
//                     <a href={`baidu://message/?id=${user.baiduHi}`}>
//                         <IconInfoflow />
//                     </a>
//                 )}
//             </UserContactContainer>
//         </CardRoot>
//     );
// }
