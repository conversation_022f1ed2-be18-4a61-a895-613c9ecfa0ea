// import styled from '@emotion/styled';
// import {ComponentProps} from 'react';
// import {Flex, Typography} from 'antd';
// import UserAvatar from '../UserAvatar';

// interface Props extends ComponentProps<typeof Flex> {
//     userName: string;
//     chineseName: string;
//     departmentName?: string;
//     positionRoles?: string | string[] | null;
// }

// const Description = styled.small`
//     color: var(--devops-gray-7);
//     display: -webkit-box;
//     -webkit-box-orient: vertical;
//     overflow: hidden;
//     white-space: pre-line;
//     word-break: break-all;
//     -webkit-line-clamp: 1;
//     flex-shrink: 0;
// `;

// const Root = styled(Flex)`
//     width: 100%;

//     > div {
//         flex: 1;
//         overflow: hidden;

//         > div {
//             flex: 1;
//             max-width: 100%;
//         }
//     }
// `;

// const renderPositionRoles = (role?: string | string[] | null) => {
//     if (Array.isArray(role)) {
//         return role.join(',');
//     }

//     return role ?? '--';
// };

// export default function User({
//     userName,
//     chineseName,
//     departmentName,
//     positionRoles,
//     ...props
// }: Props) {
//     const role = renderPositionRoles(positionRoles);
//     return (
//         <Root {...props} gap="large" align="center">
//             <UserAvatar username={userName} />
//             <Flex style={{alignItems: 'flex-start', flex: 1, overflow: 'hidden'}}>
//                 <Flex align="center">
//                     <Typography.Text>{chineseName}（{userName}）</Typography.Text>
//                 </Flex>
//                 <Flex align="center" gap="middle">
//                     {role && <Description>{role}</Description>}
//                     <Description>{departmentName || '无部门信息'}</Description>
//                 </Flex>
//             </Flex>
//         </Root>
//     );
// }
