/* eslint-disable max-lines */
import {<PERSON><PERSON>, Modal} from '@panda-design/components';
import {Dropdown, Flex, Space, Tooltip} from 'antd';
import {useBoolean} from 'huse';
import {useCallback, useState} from 'react';
import {css} from '@emotion/css';
import {create} from 'lodash';
import BDlogoIcon from '@/assets/ievalue/Bdlogo';
import BDFanyiIcon from '@/assets/ievalue/Fanyi';
import MilepostIcon from '@/assets/ievalue/Milepost';
import StaticCount from '@/icons/ievalue/StaticCount';
import {PopTextArea} from '../PopTextArea';
import {DeleteButton} from '../PopTextArea/DeleteButton';
import {usePopCreateContext} from './PopCreateProvider';
const scratchWordsPopBox = css`
    padding: 4px;
    background: transparent;
    box-sizing: border-box;
`;

const scratchWordsPopContainer = css`
    padding: 8px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 6px;
    color: #fff;
    background: #fff !important;
    overflow: visible;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    ::after {
        content: "";
        position: absolute;
        left: 45px;
        bottom: 6px;
        border-color: rgba(0, 0, 0, 0.08) transparent transparent;
        border-width: 6px 6px 0;
        border-style: solid;
        height: 0;
        width: 0;
    }
`;
export const toolProps = {
    overlayInnerStyle: {background: '#fff', color: '#000'},
    arrow: false,
};
export const PopCreate = () => {
    const [staticOpen, {on: staticOn, off: staticOff}] = useBoolean(false);
    const {onClick, sectionsText, disabled} = usePopCreateContext();

    /*
    const onCopy = useCallback(
        () => {
            copyToClipboard(sectionsText);
        },
        [sectionsText]
    );
    useEffect(
        () => {
            onCopy();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );
    */

    const search = useCallback(
        () => {
            window.open(`https://www.baidu.com/s?wd=${sectionsText}`, '_blank');
            onClick('search');
        },
        [onClick, sectionsText]
    );

    const translate = useCallback(
        () => {
            window.open(`https://fanyi.baidu.com/#en/zh/${sectionsText}`, '_blank');
            onClick('search');
        },
        [onClick, sectionsText]
    );

    const onBg = useCallback(
        (e: any) => {
            e.stopPropagation();
        },
        []
    );

    // const colors = useMemo(
    //     () => {
    //         let isEx = null;
    //         data?.tagList.forEach(element => {
    //             const tag = element.tags?.find(t => !t.tagName.length);
    //             const colors = tag?.position?.hs?.extra?.value?.[0]?.colors;
    //             if (colors) {
    //                 isEx = colors;
    //             }
    //         });
    //         return isEx;
    //     },
    //     [data?.tagList]
    // );
    // const [colorOpen, setColorOpen] = useState(false);
    const [tagOpen, setTagOpen] = useState(false);
    const [trigger, setTrigger] = useState<'click'|'hover'>('click');

    return (
        <div style={{padding: 8}} onClick={onBg}>
            <div className={scratchWordsPopBox}>
                <div className={scratchWordsPopContainer}>
                    <Space
                        split={
                            <div
                                style={{
                                    background: 'var(--color-gray-6)',
                                    width: 1,
                                    height: 14,
                                }}
                            />
                        }
                        size="small"
                        style={{gap: 2}}
                    >
                        <Tooltip title="百度搜索" {...toolProps}>
                            <Button
                                type="text"
                                size="small"
                                icon={<BDlogoIcon />}
                                onClick={search}
                            />
                        </Tooltip>
                        {/* <Flex gap={2}>
                            <Dropdown
                                placement="bottomCenter"
                                open={colorOpen}
                                onOpenChange={open => {
                                    setColorOpen(open);
                                }}
                                dropdownRender={() => {
                                    return (
                                        <ColorsTool value={colors} setColorOpen={setColorOpen} />
                                    );
                                }}
                            >
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<Word style={{width: 15, height: 15, marginBottom: 4}} />}
                                />
                            </Dropdown>
                        </Flex> */}

                        <Tooltip title="百度翻译" {...toolProps}>
                            <Button
                                type="text"
                                size="small"
                                icon={<BDFanyiIcon />}
                                onClick={translate}
                            />
                        </Tooltip>
                        <Tooltip title="字数统计" {...toolProps}>
                            <Button
                                type="text"
                                size="small"
                                style={{padding: 0}}
                                onClick={staticOn}
                            >
                                <StaticCount
                                    style={{width: 22, height: 20}}
                                />
                            </Button>
                        </Tooltip>
                        <Flex>
                            <Dropdown
                                open={tagOpen}
                                onOpenChange={open => {
                                    setTagOpen(open);
                                }}
                                trigger={[trigger]}
                                getPopupContainer={trigger => trigger.parentElement}
                                placement="bottomCenter"
                                dropdownRender={() => {
                                    return (
                                        <PopTextArea setTagOpen={setTagOpen} setTrigger={setTrigger} />
                                    );
                                }}
                            >
                                <Button
                                    type="text"
                                    size="small"
                                    icon={<MilepostIcon />}
                                    onClick={create}
                                />
                            </Dropdown>
                            {!disabled && (
                                <Tooltip title="删除划词" {...toolProps}>
                                    <DeleteButton />
                                </Tooltip>
                            )}
                        </Flex>
                    </Space>
                </div>
            </div>
            <Modal
                open={staticOpen}
                title="所选字数"
                footer={null}
                width={360}
                onCancel={staticOff}
                maskClosable={false}
            >
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: '#f7f8fa',
                        borderRadius: 8,
                        height: 88,
                    }}
                >
                    <div style={{display: 'flex', alignItems: 'baseline'}}>
                        <span
                            style={{
                                fontSize: 24,
                                fontWeight: 500,
                                lineHeight: 28,
                            }}
                        >
                            {sectionsText?.length || 0}
                        </span>
                        <span style={{fontSize: 12}}>字</span>
                    </div>
                </div>
            </Modal>
        </div>
    );
};
