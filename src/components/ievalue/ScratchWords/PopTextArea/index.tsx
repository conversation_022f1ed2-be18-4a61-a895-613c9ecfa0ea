import {Divider, Space, Tooltip, Typography} from 'antd';
import {css} from '@emotion/css';
import {useCallback, useMemo} from 'react';
import styled from '@emotion/styled';
import {DefaultOptionType} from 'antd/es/select';
import {TagItem} from '@/api/ievalue/task';
import {FlexLayout} from '@/components/ievalue/FlexLayout';
import {usePopCreateContext} from '../PopCreate/PopCreateProvider';
import {toolProps} from '../PopCreate';
import {TagSelect} from './TagSelect';
import {SaveButton} from './SaveButton';
import {DeleteButton} from './DeleteButton';
import {Remark} from './Remark';
import {TagListPanel} from './TagListPanel';
import {ColorsTool} from './ColorsTool';

const scratchWordsPopBox = css`
    padding: 7px;
    background: transparent;
    width: 350px;
    height: 480px;
    box-sizing: border-box;
`;
const scratchWordsPopContainer = css`
    position: relative;
    padding: 8px 12px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 6px;
    color: #fff;
    background: #fff;
    overflow: visible;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
        0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    .ant-space-item {
        max-width: 100%;
    }
    :after {
        content: "";
        position: absolute;
        left: 200px;
        top: 0;
        border-color: rgba(0, 0, 0, 0.08) transparent transparent;
        border-width: 0 6px 6px;
        border-style: solid;
        height: 0;
        width: 0;
        transform: translateX(-50%) translateY(100%) rotate(177deg);
    }
`;

export const Title = styled.div`
    color: #000;
    font-size: 14px;
    height: 30px;
    min-height: 30px;
`;

export const PopTextArea = ({
    setTagOpen,
    setTrigger,
}: {
    setTagOpen: (open: boolean) => void;
    setTrigger: (trigger: 'click'|'hover') => void;
}) => {
    const {data, tagList: tagListOptions, item, disabled} = usePopCreateContext();

    const onBg = useCallback(
        (e: any) => {
            e.stopPropagation();
        },
        []
    );

    const historyTags = useMemo(
        () => {
            const tags: TagItem[] = [];
            if (data?.tagList && data?.tagList.length > 0) {
                data.tagList.forEach(element => {
                    if (element.tags.length > 0) {
                        tags.push(...element.tags);
                    }
                });
            }
            return tags;
        },
        [data?.tagList]
    );
    const value = item?.hs?.extra?.value;
    const currentTags = useMemo(
        () => {
            if (!value) {
                return [];
            }
            return (
                value?.filter(
                    (item: any) => !['LIKE', 'NOTLIKE', 'SPECIAL'].includes(item)
                ) || []
            );
        },
        [value]
    );

    const tagOptions: DefaultOptionType[] = useMemo(
        () => {
            const resultTagList = tagListOptions?.filter(
                (item: TagItem) =>
                    ![
                        'LIKE',
                        'NOTLIKE',
                        'SPECIAL',
                        ...currentTags,
                        ...historyTags.map(e => e.tagName),
                    ].includes(item.tagName)
            );
            return resultTagList?.length > 0
                ? resultTagList
                : [
                    {
                        ID: 'noTag',
                        tagName: '暂无可添加标签',
                        disabled: true,
                    },
                ];
        },
        [currentTags, historyTags, tagListOptions]
    );

    return (
        <div onClick={onBg}>
            <div className={scratchWordsPopBox}>
                <div className={scratchWordsPopContainer}>
                    <FlexLayout
                        direction="column"
                        style={{width: '100%', height: '100%'}}
                    >
                        <FlexLayout
                            justify="space-between"
                            style={{width: '100%'}}
                        >
                            <Typography.Title
                                level={5}
                                style={{color: '#000', margin: 0}}
                            >
                                划词标注
                            </Typography.Title>
                            <Space>
                                {disabled ? null : (
                                    <>
                                        <Tooltip title="删除划词" {...toolProps}>
                                            <DeleteButton>删除</DeleteButton>
                                        </Tooltip>
                                        <SaveButton options={tagOptions} />
                                    </>
                                )}
                            </Space>
                        </FlexLayout>
                        <Divider style={{margin: '6px 0'}} />
                        <FlexLayout
                            direction="column"
                            style={{
                                width: '100%',
                                height: 'calc(100% - 40px)',
                                overflow: 'auto',
                                scrollbarGutter: 'stable',
                            }}
                        >
                            <ColorsTool />
                            <Remark setTrigger={setTrigger} />
                            <div style={{marginTop: 10}} />
                            <Title>添加标注</Title>
                            <TagSelect
                                options={tagOptions}
                                historyTags={historyTags}
                            />
                            <TagListPanel setTagOpen={setTagOpen} />
                        </FlexLayout>
                    </FlexLayout>
                </div>
            </div>
        </div>
    );
};
