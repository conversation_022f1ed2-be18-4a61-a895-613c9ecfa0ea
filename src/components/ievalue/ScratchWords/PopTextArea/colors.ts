export interface IColor {
    color: string;
    name: string;
}

export const defaultColor = {
    color: '#E61300',
    backgroundColor: 'transparent',
};

export const colors: IColor[] = [
    {
        color: '#fff',
        name: '皓白',
    },
    {
        color: '#878D99',
        name: '烟灰色',
    },
    {
        color: '#FFC0CB',
        name: '樱花粉',
    },
    {
        color: '#FF9999',
        name: '胭脂粉',
    },

    {
        color: '#DA70D6',
        name: '兰花粉',
    },
    {
        color: '#FFC859',
        name: '杏黄',
    },
    {
        color: '#98FF98',
        name: '薄荷绿',
    },
    {
        color: '#4FD972',
        name: '嫩绿',
    },
    {
        color: '#48D1CC',
        name: '水鸭蓝',
    },
    {
        color: '#4DA3FF',
        name: '湖蓝',
    },
    {
        color: '#8995FA',
        name: '丁香紫',
    },
    {
        color: '#E1E6F0',
        name: '月灰',
    },
    {
        color: '#1C1D1F',
        name: '玄青黑',
    },
    {
        color: '#FF6B6B',
        name: '珊瑚红',
    },
    {
        color: '#E61300',
        name: '赤红',
    },
    {
        color: '#9370DB',
        name: '中紫',
    },

    {
        color: '#FF930F',
        name: '橙黄',
    },
    {
        color: '#0DAE2F',
        name: '柳绿',
    },
    {
        color: '#2E8B57',
        name: '海绿色',
    },
    {
        color: '#2FB1B1',
        name: '深海蓝',
    },
    {
        color: '#0F6FD6',
        name: '靛蓝',
    },
    {
        color: '#4F54D6',
        name: '靛紫',
    },
];
