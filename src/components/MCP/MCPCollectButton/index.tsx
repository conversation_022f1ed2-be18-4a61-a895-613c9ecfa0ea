import {StarFilled, StarOutlined} from '@ant-design/icons';
import {Button, ButtonProps, message} from '@panda-design/components';
import {MouseEvent, CSSProperties} from 'react';
import {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';

interface Props {
    serverId: number;
    favorite: boolean;
    refresh: () => void;
    size?: ButtonProps['size'];
    style?: CSSProperties;
    showText?: boolean;
}

export const MCPCollectButton = ({
    serverId,
    refresh,
    favorite,
    size,
    style,
    showText = true,
}: Props) => {
    const handleClick = async (e: MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        if (favorite) {
            await apiDeleteServerFavorite({mcpServerId: serverId});
        } else {
            await apiPutServerFavorite({mcpServerId: serverId});
        }
        message.success(favorite ? '取消收藏成功' : '收藏成功');
        refresh();
    };
    return (
        <Button
            onClick={handleClick}
            type="text"
            size={size}
            style={style}
            icon={
                favorite
                    ? <StarFilled style={{color: '#FFA400'}} />
                    : <StarOutlined />
            }
        >
            {showText ? '收藏' : undefined}
        </Button>
    );
};
