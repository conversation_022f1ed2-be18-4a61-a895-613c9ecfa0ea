import {Flex, Popover} from 'antd';
import {css} from '@emotion/css';
import UserAvatar from '@/design/UserAvatar';

const countCss = css`
  margin-left: 4px;
  padding: 0px 4px;
  cursor: pointer;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  color: #181818;
`;

export const UserAvatarList = ({users = [], max = Infinity}: {users: string[], max: number}) => {
    const visibleUsers = max < users.length ? users.slice(0, max) : users;
    const remainingCount = users.length - max;
    const remainingUsers = remainingCount > 0 ? users.slice(max) : [];

    return (
        <Flex gap={8} wrap="nowrap" align="center">
            {visibleUsers.map((user, index) => (
                <UserAvatar key={index} username={user} showText />
            ))}
            {remainingCount > 0 && (
                <Popover
                    content={
                        <Flex gap={8} wrap="wrap" style={{maxWidth: '300px'}}>
                            {remainingUsers.map((user, index) => (
                                <UserAvatar key={index} username={user} showText />
                            ))}
                        </Flex>
                    }
                    placement="bottom"
                >
                    <span className={countCss}>
                        +{remainingCount}
                    </span>
                </Popover>
            )}
        </Flex>
    );
};
