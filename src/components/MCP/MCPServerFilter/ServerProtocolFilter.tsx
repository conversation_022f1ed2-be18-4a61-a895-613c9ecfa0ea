import {useCallback, useMemo} from 'react';
import {Form} from 'antd';
import {FilterDropdown, FilterDropdownLabel} from './FilterDropdown';

interface Props{
  value?: string;
  onChange?: (value: string) => void;
}

export const enum ServerProtocol {
    /**
     * 全部
     */
    ALL = 'all',
    /**
     * SSE
     */
    SSE = 'sse',
    /**
     * STDIO
     */
    STDIO = 'stdio',
    /**
     * Streamable HTTP
     */
    STREAMABLE_HTTP = 'streamable_http',
}

const ServerProtocolLabel = {
    [ServerProtocol.ALL]: '全部',
    [ServerProtocol.SSE]: 'SSE',
    [ServerProtocol.STDIO]: 'STDIO',
    [ServerProtocol.STREAMABLE_HTTP]: 'Streamable HTTP',
};

const options = [
    ServerProtocol.ALL,
    ServerProtocol.SSE,
    ServerProtocol.STDIO,
    ServerProtocol.STREAMABLE_HTTP,
].map(key => ({
    label: ServerProtocolLabel[key],
    key,
}));

const ServerProtocolFilter = ({value, onChange}: Props) => {
    const handleClick = useCallback(
        (key: string) => {
            return () => onChange?.(key);
        },
        [onChange]
    );
    const items = useMemo(
        () => {
            return (
                <>
                    {options.map(({key, label}) => (
                        <FilterDropdownLabel
                            key={key}
                            style={{width: '150px'}}
                            label={label}
                            selected={value === key}
                            onClick={handleClick(key)}
                        />
                    ))}
                </>
            );
        },
        [handleClick, value]
    );
    const label = useMemo(
        () => {
            return options.find(({key}) => key === value)?.label || '';
        },
        [value]
    );
    return (
        <FilterDropdown
            name="协议"
            items={items}
            value={label}
        />
    );
};

export const ServerProtocolFormItem = () => {
    return (
        <Form.Item name="serverProtocolType">
            <ServerProtocolFilter />
        </Form.Item>
    );
};
