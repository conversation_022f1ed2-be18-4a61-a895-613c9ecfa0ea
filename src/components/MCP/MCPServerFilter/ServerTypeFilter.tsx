import {useCallback, useMemo} from 'react';
import {Form} from 'antd';
import {FilterDropdown, FilterDropdownLabel} from './FilterDropdown';

interface Props{
  value?: string;
  onChange?: (value: string) => void;
}

export const enum ServerType {
    /**
     * 全部
     */
    ALL = 'all',
    /**
     * 标准MCP
     */
    STANDARD = 'external',
    /**
     * Remote
     */
    REMOTE = 'openapi',
    /**
     * Local
     */
    LOCAL = 'script',
}

const ServerTypeLabel = {
    [ServerType.ALL]: '全部',
    [ServerType.STANDARD]: '标准MCP',
    [ServerType.REMOTE]: 'Remote',
    [ServerType.LOCAL]: 'Local',
};

const options = [
    {
        label: ServerTypeLabel[ServerType.ALL],
        key: ServerType.ALL,
    },
    {
        label: ServerTypeLabel[ServerType.STANDARD],
        key: ServerType.STANDARD,
    },
    {
        label: ServerTypeLabel[ServerType.REMOTE],
        key: ServerType.REMOTE,
    },
    {
        label: ServerTypeLabel[ServerType.LOCAL],
        key: ServerType.LOCAL,
    },
];

const ServerTypeFilter = ({value, onChange}: Props) => {
    const handleClick = useCallback(
        (key: string) => {
            return () => onChange?.(key);
        },
        [onChange]
    );
    const items = useMemo(
        () => {
            return (
                <>
                    {
                        options.map(({key, label}) => (
                            <FilterDropdownLabel
                                key={key}
                                label={label}
                                selected={value === key}
                                onClick={handleClick(key)}
                            />
                        ))
                    }
                </>
            );
        },
        [handleClick, value]
    );
    const label = useMemo(
        () => {
            return options.find(({key}) => key === value)?.label || '';
        },
        [value]
    );
    return (
        <FilterDropdown
            name="类型"
            items={items}
            value={label}
        />
    );
};

export const ServerTypeFormItem = () => {
    return (
        <Form.Item name="serverSourceType">
            <ServerTypeFilter />
        </Form.Item>
    );
};
