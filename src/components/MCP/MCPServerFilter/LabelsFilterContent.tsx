import {useRequest} from 'huse';
import {useCallback, useMemo} from 'react';
import {uniq} from 'lodash';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {apiGetDefaultLabels} from '@/api/mcp';


interface Props {
    value?: number[];
    onChange?: (value: number[]) => void;
}

export const ALL_LABELS = -2;
export const OTHER_LABELS = -1;

const LabelTag = styled.div<{selected: boolean}>`
    padding: 4px 15px;
    font-size: 14px;
    cursor: pointer;
    background-color: ${props => (props.selected ? '#F1F8FF' : '#F2F2F2')};
    color: ${props => (props.selected ? '#0083FF' : '#000000')};
    border-radius: 4px;
`;

const LabelContainer = styled(Flex)`
    ${LabelTag} {
        margin-right: 12px;
    }
`;

interface LabelProps {label: string, value: number, selected: boolean, onSelect: (value: number) => void}

const Label = ({label, value, selected, onSelect}: LabelProps) => {
    window.console.log(12);
    const handleSelect = useCallback(
        () => {
            window.console.log(1);
            onSelect(value);
        },
        [value, onSelect]
    );
    return (
        <LabelTag selected={selected} onClick={handleSelect}>
            {label}
        </LabelTag>
    );
};

export const LabelsFilterContent = ({value = [], onChange}: Props) => {
    const {data} = useRequest(apiGetDefaultLabels, null);
    const options = useMemo(
        () => {
            const labels = [{label: '全部', value: ALL_LABELS}];
            data?.forEach(({labelValue, id}) => {
                labels.push({
                    label: labelValue,
                    value: id,
                });
            });
            labels.push({label: '其他', value: OTHER_LABELS});
            return labels;
        },
        [data]
    );
    const handleChange = useCallback(
        (labelValue: number) => {
            if (labelValue === ALL_LABELS || labelValue === OTHER_LABELS) {
                return onChange?.([labelValue]);
            } else if (value?.includes(labelValue)) {
                const result = value?.filter(i => i !== labelValue);
                onChange?.(result?.length > 0 ? result : [ALL_LABELS]);
            } else {
                onChange?.(uniq([...value?.filter(i => i !== ALL_LABELS && i !== OTHER_LABELS), labelValue]));
            }
        },
        [onChange, value]
    );
    return (
        <LabelContainer>
            {
                options.map(
                    option => (
                        <Label
                            key={option.value}
                            label={option.label}
                            value={option.value}
                            selected={value?.includes(option.value)}
                            onSelect={handleChange}
                        />
                    )
                )
            }
        </LabelContainer>
    );
};
