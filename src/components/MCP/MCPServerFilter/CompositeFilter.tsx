import {useCallback, useMemo} from 'react';
import {Form} from 'antd';
import {FilterDropdown, FilterDropdownLabel} from './FilterDropdown';

interface Props{
  value?: string;
  onChange?: (value: string) => void;
}

export const enum Order {
    /**
     * 综合排序
     */
    DEFAULT = 'default',
    /**
     * 最受欢迎
     */
    POPULAR = 'popular',
    /**
     * 最新发布
     */
    NEWEST = 'publishOrder',
    /**
     * 浏览量 从高到低
     */
    VIEW = 'viewOrder',
    /**
     * 调用量 从高到低
     */
    USE = 'useOrder',
    /**
     * 评论数 从高到低
     */
    COMMENT = 'commentOrder',
}

const OrderLabel = {
    [Order.DEFAULT]: '综合排序',
    [Order.POPULAR]: '最受欢迎',
    [Order.NEWEST]: '最新发布',
    [Order.VIEW]: '浏览量 从高到低',
    [Order.USE]: '调用量 从高到低',
    [Order.COMMENT]: '评论数 从高到低',
};

const options = [
    {
        label: OrderLabel[Order.DEFAULT],
        key: Order.DEFAULT,
    },
    {
        label: OrderLabel[Order.POPULAR],
        key: Order.POPULAR,
    },
    {
        label: OrderLabel[Order.NEWEST],
        key: Order.NEWEST,
    },
    {
        label: OrderLabel[Order.VIEW],
        key: Order.VIEW,
    },
    {
        label: OrderLabel[Order.USE],
        key: Order.USE,
    },
    {
        label: OrderLabel[Order.COMMENT],
        key: Order.COMMENT,
    },
];

const CompositeFilter = ({value, onChange}: Props) => {
    const handleClick = useCallback(
        (key: string) => {
            return () => onChange?.(key);
        },
        [onChange]
    );
    const items = useMemo(
        () => {
            return (
                <>
                    {
                        options.map(({key, label}) => {
                            return (
                                <FilterDropdownLabel
                                    key={key}
                                    style={{width: '160px'}}
                                    label={label}
                                    selected={value === key}
                                    onClick={handleClick(key)}
                                />
                            );
                        })
                    }
                </>
            );
        },
        [handleClick, value]
    );
    const label = useMemo(
        () => {
            return options.find(({key}) => key === value)?.label ?? '';
        },
        [value]
    );
    return (
        <FilterDropdown
            name=""
            items={items}
            value={label}
        />
    );
};

export const CompositeFilterFormItem = () => {
    return (
        <Form.Item name="order">
            <CompositeFilter />
        </Form.Item>
    );
};
