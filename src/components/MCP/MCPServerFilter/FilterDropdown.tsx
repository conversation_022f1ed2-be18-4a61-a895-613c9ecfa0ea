import {CheckOutlined} from '@ant-design/icons';
import {IconDownSolid} from '@baidu/ee-icon';
import styled from '@emotion/styled';
import {Popover, Flex} from 'antd';
import {CSSProperties, ReactElement, useMemo, useState} from 'react';

const StyledFlex = styled(Flex)<{actived: boolean}>`
    padding: 2px 7px;
    cursor: pointer;
    .name{
      color: #5C5C5C;
      font-size: 14px;
    }
    .value{
      color: #000000;
      font-size: 14px;
      margin-left: 12px;
    }
    .triangle{
      color: #BFBFBF;
      margin-top: 5px;
      margin-left: 10px;
      transform: ${props => (props.actived ? 'rotate(180deg)' : 'rotate(0deg)')};
    }
`;

const StyledPopover = styled(Popover)`
  .ant-5-popover-inner{
    padding: 10px 5px;
  }
`;

interface Props {
  items: ReactElement;
  name: string;
  value?: string;
}

export const FilterDropdown = ({items, name, value}: Props) => {
    const [open, setOpen] = useState(false);
    const content = useMemo(
        () => {
            return (
                <Flex vertical>
                    {items}
                </Flex>
            );
        },
        [items]
    );
    return (
        <StyledPopover
            content={content}
            trigger={['click']}
            open={open}
            onOpenChange={setOpen}
            placement="bottom"
        >
            <StyledFlex actived={open}>
                <span className="name">{name}</span>
                {
                    value && <span className="value">{value}</span>
                }
                <IconDownSolid className="triangle" />
            </StyledFlex>
        </StyledPopover>
    );
};

interface LabelProps {
  label: string;
  selected: boolean;
  onClick: () => void;
  style?: CSSProperties;
  className?: string;
}

const FilterItem = styled(Flex)<{selected: boolean}>`
  padding: 6px 12px;
  cursor: pointer;
  background-color: ${props => (props.selected ? '#f2f2f2' : 'transparent')};
  border-radius: 4px;
`;

export const FilterDropdownLabel = ({label, selected, onClick, style, className}: LabelProps) => {
    return (
        <FilterItem justify="space-between" selected={selected} onClick={onClick} style={style} className={className}>
            <span>{label}</span>
            {selected && <CheckOutlined />}
        </FilterItem>
    );
};
