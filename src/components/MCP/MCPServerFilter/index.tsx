/* eslint-disable max-lines */
import {Flex, Form, Input} from 'antd';
import {SearchOutlined} from '@ant-design/icons';
import styled from '@emotion/styled';
import {CSSProperties} from 'react';
import TitleTabs from '@/components/MCP/TitleTabs';
import {MCPPlaygoundButton} from '@/components/MCP/MCPPlaygoundButton';
import {CreateMCPButton} from '@/components/MCP/CreateMCPButton';
import {useFetchMCPServersContext} from '../MCPServerCard/FetchMCPServersProvider';
import {LabelsFilterContent} from './LabelsFilterContent';
import {ServerType, ServerTypeFormItem} from './ServerTypeFilter';
import {ServerProtocol, ServerProtocolFormItem} from './ServerProtocolFilter';
import {CompositeFilterFormItem, Order} from './CompositeFilter';

const items = [
    // {
    //     label: '精选',
    //     key: 'id1',
    // },
    {
        label: '全部',
        key: 'all',
    },
    {
        label: '我收藏的',
        key: 'favorite',
    },
    {
        label: '我发布的',
        key: 'isMine',
    },
];

export interface FilterValues {
    serverSourceType: ServerType;
    serverProtocolType: ServerProtocol;
    labels: number[];
    order: Order;
}

export interface TabValues {
    tab: string;
    keywords?: string;
    favorite?: boolean;
    isMine?: boolean;
}

const StyledForm = styled(Form)`
    .ant-5-form-item {
        margin-bottom: 0;
    }
    margin-top: 16px;
`;

interface Props {
    initialTabFormValue: TabValues;
    initialFilterFormValue: FilterValues;
    style?: CSSProperties;
    className?: string;
}
const MCPServerFilter = ({initialTabFormValue, initialFilterFormValue, style, className}: Props) => {
    const [tabForm] = Form.useForm();
    const [filterForm] = Form.useForm();
    const {changeSearchParams} = useFetchMCPServersContext();
    const onFormChange = (name: string, info: any) => {
        const changed = {
            [info.changedFields[0].name[0]]: info.changedFields[0].value,
        };
        changeSearchParams(changed);
    };
    return (
        <Form.Provider onFormChange={onFormChange}>
            <div style={style} className={className}>
                <Form
                    name="tab"
                    form={tabForm}
                    initialValues={initialTabFormValue}
                >
                    <Form.Item name="tab" noStyle>
                        <TitleTabs
                            tabBarExtraContent={(
                                <Flex align="center" gap={8} style={{marginTop: 8}}>
                                    <Form.Item name="keywords" noStyle>
                                        <Input
                                            autoFocus
                                            style={{width: 250}}
                                            placeholder="输入MCP Server名称"
                                            suffix={<SearchOutlined />}
                                            allowClear
                                        />
                                    </Form.Item>
                                    <CreateMCPButton />
                                    <MCPPlaygoundButton />
                                </Flex>
                            )}
                            items={items}
                        />
                    </Form.Item>
                </Form>
                <StyledForm
                    form={filterForm}
                    initialValues={initialFilterFormValue}
                    name="filter"
                >
                    <Form.Item name="labels">
                        <LabelsFilterContent />
                    </Form.Item>
                    <Flex justify="space-between" style={{marginTop: '16px'}}>
                        <Flex>
                            <ServerTypeFormItem />
                            <ServerProtocolFormItem />
                        </Flex>
                        <Flex>
                            <CompositeFilterFormItem />
                        </Flex>
                    </Flex>
                </StyledForm>
            </div>
        </Form.Provider>
    );
};

export default MCPServerFilter;
