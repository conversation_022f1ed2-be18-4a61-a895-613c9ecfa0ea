import {createComatestackLink} from '@/links/createLink';
interface ParamsMcp {
    mcpId?: number;
    workspaceId?: number;
    applicationId?: number;
    type?: 'server' | 'app';
}

interface ParamsMcpDetail {
    mcpId?: number;
    workspaceId?: number;
    tab?: 'overview' | 'tools';
    toolId?: number;
}

export const AIToolsHomeLink = createComatestackLink('/mcp');
export const MCPSquareLink = createComatestackLink('/mcp/square');
export const MCPDetailLink = createComatestackLink<ParamsMcpDetail>(
    '/mcp/square/server/{mcpId}'
);
export const MCPSpaceLink = createComatestackLink<ParamsMcp>(
    '/mcp/space/{workspaceId}'
);
export const MCPSpaceDetailLink = createComatestackLink<ParamsMcpDetail>(
    '/mcp/space/{workspaceId}/server/{mcpId}'
);
export const MCPSpaceSettingLink = createComatestackLink<ParamsMcp>(
    '/mcp/space/{workspaceId}/setting'
);

export const MCPApplicationLink = createComatestackLink<ParamsMcp>(
    '/mcp/space/{workspaceId}/application/{applicationId}'
);

interface MCPEditParams extends ParamsMcp {
    activeTab?: 'basicInfo' | 'tools';
}

export const MCPEditLink = createComatestackLink<MCPEditParams>(
    '/mcp/space/{workspaceId}/server/{mcpId}/edit'
);

interface CreateParams{
    workspaceId?: number;
    type?: string;
}

export const MCPCreateLink = createComatestackLink<CreateParams>(
    '/mcp/space/{workspaceId}/create'
);
export const MCPCreateNoSpaceLink = createComatestackLink<CreateParams>(
    '/mcp/space/create'
);

export const MCPPlaygroundLink = createComatestackLink<{serverId?: number}>(
    '/mcp/square/playground'
);

export const MCPZoneLink = createComatestackLink<{zoneId?: string}>(
    '/mcp/square/zone/{zoneId}'
);
