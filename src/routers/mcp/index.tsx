/* eslint-disable max-lines */
import {RouteObject} from '@panda-design/router';
import {lazy} from 'react';
import {MCPSpaceBreadCrumbButton} from '@/components/MCP/MCPSpaceBreadCrumbButton';
import {
    MCPSquareBreadCrumbLink,
    MCPApplicationDetailBreadCrumbLink,
    MCPServerDetailBreadCrumbLink,
    MCPPlaygroundBreadCrumbLink,
} from './MCPSquareBreadCrumbLink';

const MCPLayout = lazy(
    () => import(/* webpackChunkName: "MCPLayout" */ '../AppLayout/MCPLayout')
);

const MCPCheckAuthLayout = lazy(
    () =>
        import(
            /* webpackChunkName: "MCPCheckAuthLayout" */ '@/comatestack/MCP/MCPCheckAuthLayout'
        )
);
const MCPSquare = lazy(
    () =>
        import(
            /* webpackChunkName: "MCPSquare" */ '@/comatestack/MCP/MCPSquare'
        )
);
const AIToolsHome = lazy(
    () =>
        import(
            /* webpackChunkName: "AIToolsHome" */ '@/comatestack/MCP/AIToolsHome'
        )
);
const MCPSquireDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "MCPSquireDetail" */ '@/comatestack/MCP/MCPSquireDetail'
        )
);
const MCPSpace = lazy(
    () =>
        import(/* webpackChunkName: "MCPSpace" */ '@/comatestack/MCP/MCPSpace')
);
const MCPEdit = lazy(
    () => import(/* webpackChunkName: "MCPEdit" */ '@/comatestack/MCP/MCPEdit')
);
const MCPCreate = lazy(
    () =>
        import(/* webpackChunkName: "MCPEdit" */ '@/comatestack/MCP/MCPCreate')
);
const MCPSpaceSetting = lazy(
    () =>
        import(
            /* webpackChunkName: "MCPSpaceSetting" */ '@/comatestack/MCP/MCPSpaceSetting'
        )
);

const MCPApplicationDetail = lazy(
    () =>
        import(
            /* webpackChunkName: "MCPApplicationDetail" */ '@/comatestack/MCP/MCPApplicationDetail'
        )
);

const MCPPlayground = lazy(
    () =>
        import(
            /* webpackChunkName: "MCPPlayground" */ '@/mcp/Playground'
        )
);

const MCPZone = lazy(
    () => import(/* webpackChunkName: "MCPZone" */ '@/comatestack/MCP/MCPZone')
);

export const mcpRoutes: RouteObject[] = [
    {
        path: 'comatestack',
        element: <MCPLayout />,
        children: [
            {
                path: 'mcp/*',
                children: [
                    {
                        path: '',
                        documentTitle: 'AI Tools',
                        element: <AIToolsHome />,
                    },
                    {
                        path: 'square',
                        documentTitle: 'MCP广场',
                        breadcrumb: <MCPSquareBreadCrumbLink />,
                        children: [
                            {
                                path: '',
                                element: <MCPSquare />,
                            },
                            {
                                breadcrumb: <MCPServerDetailBreadCrumbLink />,
                                path: 'server/:mcpId/*',
                                element: <MCPSquireDetail />,
                            },
                            {
                                path: 'playground',
                                breadcrumb: <MCPPlaygroundBreadCrumbLink />,
                                element: <MCPPlayground />,
                            },
                            {
                                path: 'zone/:zoneId',
                                element: <MCPZone />,
                            },
                        ],
                    },
                    {
                        path: 'space/create',
                        breadcrumb: <MCPSquareBreadCrumbLink />,
                        children: [
                            {
                                path: '',
                                documentTitle: '新建MCP',
                                element: <MCPCreate />,
                            },
                        ],
                    },
                    {
                        path: 'space/:workspaceId/*',
                        breadcrumb: <MCPSpaceBreadCrumbButton />,
                        element: <MCPCheckAuthLayout />,
                        children: [
                            {
                                path: '',
                                documentTitle: 'MCP',
                                element: <MCPSpace />,
                            },
                            {
                                breadcrumb: <MCPServerDetailBreadCrumbLink />,
                                path: 'server/:mcpId/*',
                                element: <MCPSquireDetail />,
                            },
                            {
                                path: 'setting',
                                documentTitle: '空间设置',
                                element: <MCPSpaceSetting />,
                            },
                            {
                                breadcrumb: <MCPApplicationDetailBreadCrumbLink />,
                                path: 'application/:applicationId',
                                documentTitle: '应用详情',
                                element: <MCPApplicationDetail />,
                            },
                            {
                                path: 'create',
                                breadcrumb: <MCPSquareBreadCrumbLink />,
                                children: [
                                    {
                                        path: '',
                                        documentTitle: '新建MCP',
                                        element: <MCPCreate />,
                                    },
                                ],
                            },
                            {
                                path: 'server/:mcpId/edit',
                                breadcrumb: <MCPServerDetailBreadCrumbLink />,
                                children: [
                                    {
                                        path: '',
                                        documentTitle: 'MCP编辑',
                                        element: <MCPEdit />,
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
    },
];
