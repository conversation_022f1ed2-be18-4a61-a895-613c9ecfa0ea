import type { SVGProps } from "react";
const SvgComment = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 12 12"
        {...props}
    >
        <path
            fill="#545454"
            d="M8.8 2.683c.39 0 .706.316.706.706v5.45c0 .389-.317.704-.706.704H6.68a.27.27 0 0 0-.194.081l-1.293 1.292-1.292-1.292a.28.28 0 0 0-.193-.08H1.587a.706.706 0 0 1-.706-.706v-5.45c0-.389.316-.705.706-.705zm-6.86.784a.274.274 0 0 0-.275.274v4.745c0 .15.123.274.274.274h1.915c.187 0 .366.074.499.206l.84.84.842-.84a.7.7 0 0 1 .5-.206h1.912a.275.275 0 0 0 .275-.274V3.74a.274.274 0 0 0-.275-.274zm1.489 2.156a.589.589 0 1 1 0 1.178.589.589 0 0 1 0-1.178m1.764 0a.588.588 0 1 1 0 1.176.588.588 0 0 1 0-1.176m1.765 0a.588.588 0 1 1 0 1.177.588.588 0 0 1 0-1.177"
        />
        <path
            fill="#545454"
            d="M10.331 7.162V2.756a.92.92 0 0 0-.919-.919H3.975a.394.394 0 1 1 0-.787h5.437c.943 0 1.706.764 1.706 1.706v4.406a.394.394 0 1 1-.787 0"
        />
    </svg>
);
export default SvgComment;
