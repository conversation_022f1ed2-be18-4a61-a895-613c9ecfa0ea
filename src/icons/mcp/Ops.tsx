import type { SVGProps } from "react";
const SvgOps = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 26 26"
        {...props}
    >
        <g clipPath="url(#ops_svg__a)">
            <path
                fill="url(#ops_svg__b)"
                d="M3.23 3.228c1.34-.828 3.129-.382 3.895 1.019a2.76 2.76 0 0 1-1.021 3.882 3.07 3.07 0 0 1-2.236.255c-1.532 2.929-1.532 6.621.256 9.677 2.235 3.82 6.578 5.665 10.729 4.901.51-.063.957.192 1.085.764.127.573-.192 1.083-.766 1.21-4.917.891-10.09-1.273-12.708-5.856C.293 15.324.42 10.867 2.336 7.302c-.064-.063-.064-.127-.128-.191a2.76 2.76 0 0 1 1.021-3.883m7.6-2.164A12.2 12.2 0 0 1 23.536 6.92c2.171 3.756 2.044 8.213.128 11.778.***************.127.127a2.76 2.76 0 0 1-1.022 3.883c-1.34.827-3.129.382-3.895-1.019a2.76 2.76 0 0 1 1.023-3.883 3.07 3.07 0 0 1 2.234-.255c1.532-2.928 1.533-6.62-.255-9.676-2.235-3.819-6.578-5.666-10.729-4.902a.994.994 0 0 1-1.085-.7c-.128-.573.191-1.082.766-1.209m11.302 18.78c-.256-.446-.83-.574-1.277-.32-.447.256-.575.829-.32 1.274.256.446.83.573 1.277.319.447-.255.575-.828.32-1.274M12.68 5.822a.55.55 0 0 1 .638 0l2.874 1.779c.192.137.32.343.32.616v2.396c0 .273.127.48.319.616l1.916 1.163a.73.73 0 0 1 .318.616v3.559c0 .273-.127.48-.318.616l-2.874 1.78a.55.55 0 0 1-.639 0L13 17.594l-2.234 1.368a.55.55 0 0 1-.639 0l-2.874-1.779a.73.73 0 0 1-.32-.616v-3.559c0-.274.128-.479.32-.616l1.916-1.163a.73.73 0 0 0 .32-.616V8.218c0-.274.127-.48.319-.616zM8.85 13.83v1.984l1.596.959 1.596-.96v-1.983l-1.596-.958zm5.108 0v1.984l1.597.959 1.596-.96v-1.983l-1.596-.958zM11.404 9.04v1.984l1.596.959 1.597-.96V9.04L13 8.082zm-5.94-3.902c-.255-.445-.83-.573-1.277-.318s-.574.827-.319 1.272c.256.446.83.573 1.278.319.446-.255.574-.827.319-1.273"
            />
        </g>
        <defs>
            <linearGradient
                id="ops_svg__b"
                x1={2.081}
                x2={25.133}
                y1={4.507}
                y2={20.28}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#6DA6FF" />
                <stop offset={0.5} stopColor="#BBD4FF" />
                <stop offset={1} stopColor="#7E7DFF" />
            </linearGradient>
            <clipPath id="ops_svg__a">
                <path fill="#fff" d="M0 0h26v26H0z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgOps;
