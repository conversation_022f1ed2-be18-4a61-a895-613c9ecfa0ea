import type { SVGProps } from "react";
const SvgDev = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 26 26"
        {...props}
    >
        <path
            fill="url(#dev_svg__a)"
            d="M24.03 1.97a1.103 1.103 0 0 1 1.103 1.103v19.854a1.103 1.103 0 0 1-1.103 1.103H1.97a1.103 1.103 0 0 1-1.104-1.103V3.073A1.103 1.103 0 0 1 1.97 1.97zm-1.103 2.206H3.072v17.648h19.855zm-9.56 2.763.978.11a.55.55 0 0 1 .486.622l-1.418 10.364a.55.55 0 0 1-.607.474l-.978-.11a.55.55 0 0 1-.486-.622L12.76 7.412a.55.55 0 0 1 .606-.473m-4.179 2.73.692.642a.553.553 0 0 1 .019.79l-1.903 1.945L9.9 14.992a.55.55 0 0 1-.009.78l-.01.01-.692.642a.55.55 0 0 1-.767-.016l-2.953-2.973a.55.55 0 0 1 0-.777L8.42 9.684a.55.55 0 0 1 .767-.015m8.39.015 2.954 2.974a.55.55 0 0 1 0 .777l-2.954 2.973a.55.55 0 0 1-.767.016l-.692-.642a.55.55 0 0 1-.019-.79l1.903-1.946-1.903-1.945a.55.55 0 0 1 .02-.79l.691-.642a.55.55 0 0 1 .767.015"
        />
        <defs>
            <linearGradient
                id="dev_svg__a"
                x1={1.087}
                x2={23.588}
                y1={2.412}
                y2={23.59}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#0EA5F2" />
                <stop offset={0.49} stopColor="#BAF2FF" />
                <stop offset={1} stopColor="#6DACF8" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgDev;
