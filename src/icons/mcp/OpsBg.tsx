import type { SVGProps } from "react";
const SvgOpsBg = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 384 132"
        {...props}
    >
        <mask
            id="opsBg_svg__c"
            width={384}
            height={132}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
                maskType: "alpha",
            }}
        >
            <rect
                width={383}
                height={131}
                x={0.5}
                y={0.5}
                fill="url(#opsBg_svg__a)"
                stroke="url(#opsBg_svg__b)"
                rx={3.5}
            />
        </mask>
        <g mask="url(#opsBg_svg__c)">
            <rect
                width={383}
                height={131}
                x={0.5}
                y={0.5}
                fill="url(#opsBg_svg__d)"
                stroke="url(#opsBg_svg__e)"
                rx={3.5}
            />
            <g filter="url(#opsBg_svg__f)" opacity={0.2}>
                <ellipse
                    cx={49.246}
                    cy={119.5}
                    fill="#C1CFFF"
                    fillOpacity={0.3}
                    rx={41.287}
                    ry={41.5}
                />
            </g>
        </g>
        <defs>
            <linearGradient
                id="opsBg_svg__a"
                x1={410.585}
                x2={-24.569}
                y1={-27}
                y2={121.818}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#DEF3FF" />
                <stop offset={1} stopColor="#fff" />
            </linearGradient>
            <linearGradient
                id="opsBg_svg__b"
                x1={426.831}
                x2={-35.713}
                y1={-63}
                y2={159.838}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4CA0EC" />
                <stop offset={1} stopColor="#fff" />
            </linearGradient>
            <linearGradient
                id="opsBg_svg__d"
                x1={457.558}
                x2={1.489}
                y1={-29.281}
                y2={124.662}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#EEF" stopOpacity={0.9} />
                <stop offset={1} stopColor="#fff" />
            </linearGradient>
            <linearGradient
                id="opsBg_svg__e"
                x1={911.754}
                x2={-0.342}
                y1={-177}
                y2={123.489}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#BBBCFF" />
                <stop offset={1} stopColor="#F8F8F8" />
            </linearGradient>
            <filter
                id="opsBg_svg__f"
                width={114.174}
                height={114.6}
                x={-7.841}
                y={62.2}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feBlend
                    in="SourceGraphic"
                    in2="BackgroundImageFix"
                    result="shape"
                />
                <feGaussianBlur
                    result="effect1_foregroundBlur_1564_25989"
                    stdDeviation={7.9}
                />
            </filter>
        </defs>
    </svg>
);
export default SvgOpsBg;
