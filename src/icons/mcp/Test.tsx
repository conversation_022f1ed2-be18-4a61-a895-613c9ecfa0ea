import type { SVGProps } from "react";
const SvgTest = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 26 26"
        {...props}
    >
        <path
            fill="url(#test_svg__a)"
            d="M11.027 20.528a7.9 7.9 0 0 0 2.258 2.413v.002h.002a7.9 7.9 0 0 0 2.698 1.204c-1.134.765-2.44 1.2-3.829 1.2-2.78 0-5.223-1.741-6.632-4.376-.979.009-1.555.218-1.815.545-.36.455-.553.98-.585 1.609a1.13 1.13 0 1 1-2.256-.115c.056-1.092.414-2.067 1.07-2.896.61-.77 1.53-1.204 2.712-1.348a11.3 11.3 0 0 1-.343-1.885h-2.31a1.128 1.128 0 1 1 0-2.258h2.311c.072-.753.22-1.497.44-2.221-1.009-.183-1.802-.604-2.347-1.293-.657-.828-1.016-1.804-1.072-2.895a1.13 1.13 0 0 1 2.256-.115c.031.629.226 1.153.585 1.608.23.293.716.49 1.516.535a9 9 0 0 1 2.017-2.418 5.08 5.08 0 1 1 9.155.215q.275.25.532.525a7.9 7.9 0 0 0-2.704.627 4.8 4.8 0 0 0-1.401-.63v1.41a7.9 7.9 0 0 0-2.258 2.412V8.562c-2.576.68-4.515 3.64-4.515 7.19s1.94 6.511 4.515 7.191zM20.49 9.023a3 3 0 0 0 .19-.924 1.13 1.13 0 0 1 2.255.115 5.1 5.1 0 0 1-.447 1.88 7.9 7.9 0 0 0-1.998-1.07M9.661 6.645a6.64 6.64 0 0 1 5.295.132 2.823 2.823 0 1 0-5.295-.131zm12.502 13.541 1.874 2.4a1.129 1.129 0 0 1-1.779 1.389l-1.773-2.27a5.644 5.644 0 1 1 1.678-1.517zm-4.962.02a3.386 3.386 0 1 0 .892-6.712 3.386 3.386 0 0 0-.892 6.713"
        />
        <defs>
            <linearGradient
                id="test_svg__a"
                x1={5.249}
                x2={24.215}
                y1={4.419}
                y2={24.609}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#A292FF" />
                <stop offset={0.452} stopColor="#FBCAFF" />
                <stop offset={1} stopColor="#DB57FF" />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgTest;
