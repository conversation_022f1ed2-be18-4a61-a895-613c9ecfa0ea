<svg width="384" height="132" viewBox="0 0 384 132" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_1564_26008" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="384" height="132">
<rect x="0.5" y="0.5" width="383" height="131" rx="3.5" fill="url(#paint0_linear_1564_26008)" stroke="url(#paint1_linear_1564_26008)"/>
</mask>
<g mask="url(#mask0_1564_26008)">
<rect x="0.5" y="0.5" width="383" height="131" rx="3.5" fill="url(#paint2_linear_1564_26008)" stroke="url(#paint3_linear_1564_26008)"/>
<g opacity="0.2" filter="url(#filter0_f_1564_26008)">
<ellipse cx="49.2462" cy="119.5" rx="41.2872" ry="41.5" fill="#E3A8FF" fill-opacity="0.3"/>
</g>
</g>
<defs>
<filter id="filter0_f_1564_26008" x="-7.84102" y="62.2" width="114.174" height="114.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7.9" result="effect1_foregroundBlur_1564_26008"/>
</filter>
<linearGradient id="paint0_linear_1564_26008" x1="410.585" y1="-27" x2="-24.5688" y2="121.818" gradientUnits="userSpaceOnUse">
<stop stop-color="#DEF3FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_1564_26008" x1="426.831" y1="-63" x2="-35.7126" y2="159.838" gradientUnits="userSpaceOnUse">
<stop stop-color="#4CA0EC"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_1564_26008" x1="679.385" y1="-94" x2="0.979389" y2="122.822" gradientUnits="userSpaceOnUse">
<stop stop-color="#F3ECFF" stop-opacity="0.9"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_1564_26008" x1="873.354" y1="-282" x2="-4.15483" y2="88.1502" gradientUnits="userSpaceOnUse">
<stop stop-color="#E1AAFF"/>
<stop offset="1" stop-color="#F8F8F8"/>
</linearGradient>
</defs>
</svg>
