<svg width="384" height="132" viewBox="0 0 384 132" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_1564_25989" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="384" height="132">
<rect x="0.5" y="0.5" width="383" height="131" rx="3.5" fill="url(#paint0_linear_1564_25989)" stroke="url(#paint1_linear_1564_25989)"/>
</mask>
<g mask="url(#mask0_1564_25989)">
<rect x="0.5" y="0.5" width="383" height="131" rx="3.5" fill="url(#paint2_linear_1564_25989)" stroke="url(#paint3_linear_1564_25989)"/>
<g opacity="0.2" filter="url(#filter0_f_1564_25989)">
<ellipse cx="49.2462" cy="119.5" rx="41.2872" ry="41.5" fill="#C1CFFF" fill-opacity="0.3"/>
</g>
</g>
<defs>
<filter id="filter0_f_1564_25989" x="-7.84102" y="62.2" width="114.174" height="114.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7.9" result="effect1_foregroundBlur_1564_25989"/>
</filter>
<linearGradient id="paint0_linear_1564_25989" x1="410.585" y1="-27" x2="-24.5688" y2="121.818" gradientUnits="userSpaceOnUse">
<stop stop-color="#DEF3FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_1564_25989" x1="426.831" y1="-63" x2="-35.7126" y2="159.838" gradientUnits="userSpaceOnUse">
<stop stop-color="#4CA0EC"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_1564_25989" x1="457.558" y1="-29.2806" x2="1.48944" y2="124.662" gradientUnits="userSpaceOnUse">
<stop stop-color="#EEEEFF" stop-opacity="0.9"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_1564_25989" x1="911.754" y1="-177" x2="-0.342359" y2="123.489" gradientUnits="userSpaceOnUse">
<stop stop-color="#BBBCFF"/>
<stop offset="1" stop-color="#F8F8F8"/>
</linearGradient>
</defs>
</svg>
