import type { SVGProps } from "react";
const SvgSubscribe2 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <g clipPath="url(#subscribe2_svg__a)">
            <path fill="#D8D8D8" d="M0 0h16v16H0z" opacity={0.01} />
            <path
                fill="#5C5C5C"
                d="M4 1h3.333a.667.667 0 1 1 0 1.333H4a.667.667 0 0 0-.665.617L3.333 3v10.508l3.373-2.248a2.33 2.33 0 0 1 2.509-.05l.08.05 3.372 2.249V7.667a.667.667 0 1 1 1.333 0v6.464a1 1 0 0 1-1.555.833l-3.89-2.594a1 1 0 0 0-1.11 0l-3.89 2.594A1 1 0 0 1 2 14.13V3.001a2 2 0 0 1 2-2m4.667 3a.667.667 0 0 1 .666-.667H11V1.667a.667.667 0 1 1 1.333 0v1.666H14a.667.667 0 1 1 0 1.334h-1.667v1.666a.667.667 0 1 1-1.333 0V4.667H9.333A.667.667 0 0 1 8.667 4"
            />
        </g>
        <defs>
            <clipPath id="subscribe2_svg__a">
                <path fill="#fff" d="M0 0h16v16H0z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgSubscribe2;
