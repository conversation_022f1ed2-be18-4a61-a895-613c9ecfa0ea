import type { SVGProps } from "react";
const SvgMcp = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <g clipPath="url(#mcp_svg__a)">
            <path fill="#fff" d="M0 0h16v16H0z" />
            <path
                fill="#5C5C5C"
                d="M3.318 4.247A5.99 5.99 0 0 1 8 1.999a5.99 5.99 0 0 1 4.682 2.248h1.62A7.33 7.33 0 0 0 8 .666a7.33 7.33 0 0 0-6.302 3.58zm0 7.504h-1.62A7.33 7.33 0 0 0 8 15.333a7.33 7.33 0 0 0 6.302-3.582h-1.62A5.99 5.99 0 0 1 8 14a5.99 5.99 0 0 1-4.682-2.248M15.363 6c.315 0 .484.169.484.484v1.768c-.023.286-.264.491-.477.491h-2.303q-.145.002-.147.147V9.93c0 .14-.081.22-.22.22h-.953c-.14 0-.22-.08-.22-.22V6.535a.32.32 0 0 0-.052-.176l-.059-.096c-.095-.146-.03-.264.147-.264zm-.925.851c0-.095-.05-.147-.147-.147h-1.224c-.096 0-.147.052-.147.147v1.041c0 .***************.147h1.224q.145-.002.147-.147zm-3.526 3.08c0 .14-.08.22-.22.22H6.973c-.256 0-.498-.161-.498-.491v-3.22c0-.286.153-.44.44-.44h3.776c.14 0 .22.081.22.22v.264c0 .14-.08.22-.22.22H7.993c-.095 0-.146.052-.146.147V9.3q.002.144.146.146h2.376a.23.23 0 0 0 .176-.073l.096-.096c.14-.139.271-.088.271.11zM.393 6.22C.313 6.094.393 6 .57 6H1.86c.125 0 .205.058.257.168l.968 2.207c.***************.198 0l.938-2.207c.052-.11.132-.169.257-.169h1.093c.14 0 .22.081.22.22V9.93c0 .14-.081.22-.22.22h-.8c-.139 0-.22-.08-.22-.22V8.39c0-.147-.11-.176-.168-.037l-.594 1.35c-.052.11-.132.168-.257.168h-.763c-.124 0-.205-.058-.256-.168l-.565-1.276c-.059-.14-.169-.11-.169.036V9.93c0 .14-.08.22-.22.22H.753c-.139 0-.22-.08-.22-.22V6.535a.33.33 0 0 0-.05-.176z"
            />
        </g>
        <defs>
            <clipPath id="mcp_svg__a">
                <path fill="#fff" d="M0 0h16v16H0z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgMcp;
