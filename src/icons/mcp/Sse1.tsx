import type { SVGProps } from "react";
const SvgSse1 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 12 12"
        {...props}
    >
        <path
            fill="#8F8F8F"
            d="M6.003 2.065a4.11 4.11 0 0 1 3.84 2.654 2.567 2.567 0 0 1-.658 5.047h-6.37a2.566 2.566 0 0 1-.653-5.05 4.11 4.11 0 0 1 3.84-2.65m-.001.975a3.1 3.1 0 0 0-2.47 1.207 3.1 3.1 0 0 0-.458.814l-.178.47-.487.127a1.6 1.6 0 0 0-.847.56A1.59 1.59 0 0 0 2.816 8.79h6.368a1.58 1.58 0 0 0 1.124-.465c.3-.3.466-.701.466-1.126 0-.721-.487-1.354-1.182-1.541l-.486-.129-.176-.468A3.156 3.156 0 0 0 7.8 3.606 3.1 3.1 0 0 0 6 3.04"
        />
        <rect
            width={4.381}
            height={0.876}
            x={3.809}
            y={6.884}
            fill="#8F8F8F"
            rx={0.219}
        />
        <rect
            width={4.381}
            height={0.876}
            x={3.809}
            y={5.241}
            fill="#8F8F8F"
            rx={0.219}
        />
    </svg>
);
export default SvgSse1;
